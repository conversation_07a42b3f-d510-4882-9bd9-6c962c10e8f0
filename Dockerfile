FROM node:22-bookworm-slim

RUN node -v
RUN apt-get update && apt-get install -y \
    ca-certificates fonts-liberation libasound2 libatk-bridge2.0-0 \
    libatk1.0-0 libcups2 libdbus-1-3 libdrm2 libgbm1 libgtk-3-0 \
    libnspr4 libnss3 libx11-xcb1 libxcomposite1 libxdamage1 libxrandr2 \
    xdg-utils wget --no-install-recommends && rm -rf /var/lib/apt/lists/*
WORKDIR /app   
 
COPY package*.json ./

RUN npm config set registry https://registry.npmjs.org/

RUN npm install
COPY . .

ENV NODE_TLS_REJECT_UNAUTHORIZED=0

RUN npm run build 

CMD ["node", ".output/server/index.mjs"]