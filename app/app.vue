<template>
  <div>
    <NuxtRouteAnnouncer />
    <NuxtLoadingIndicator color="#9cc2e8" :height="5" :throttle="0" />
    <NuxtLayout>
      <NuxtPage
        :transition="{
          name: 'slide-right',
          mode: 'out-in',
        }"
      />
    </NuxtLayout>
  </div>
</template>

<style>
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition:
    opacity 0.2s,
    transform 0.2s;
  will-change: opacity, transform;
}

.slide-left-enter-from {
  opacity: 0;
  transform: translateX(50px);
}
.slide-left-leave-to {
  opacity: 0;
  transform: translateX(-50px);
}
.slide-right-enter-from {
  opacity: 0;
  transform: translateX(-50px);
}
.slide-right-leave-to {
  opacity: 0;
  transform: translateX(50px);
}
</style>
