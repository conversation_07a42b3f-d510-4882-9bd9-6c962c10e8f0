<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from "vue";
import style from "../../assets/cytoscape-style.json";
import cytoscape from "cytoscape";
import undoRedo from "cytoscape-undo-redo";
import nodeHtmlLabel from "cytoscape-node-html-label";
import animateFlow from "cytoscape-animate-flow";

if (typeof cytoscape("core", "undoRedo") === "undefined") {
  undoRedo(cytoscape);
}
if (typeof cytoscape("core", "nodeHtmlLabel") === "undefined") {
  nodeHtmlLabel(cytoscape);
}
if (typeof cytoscape("core", "animateFlow") === "undefined") {
  animateFlow(cytoscape);
}
interface MyCytoscapeOptions extends cytoscape.CytoscapeOptions {
  renderer?: Partial<{
    name: string;
    webgl: boolean;
    showFps: boolean;
    webglDebug: boolean;
    webglTexSize: number;
    webglTexRows: number;
    webglBatchSize: number;
    webglTexPerBatch: number;
  }>;
}

const cyRef = ref<HTMLDivElement | null>(null);
const cyInstance = ref<cytoscape.Core | null>(null);
const props = defineProps<{
  elements?: cytoscape.ElementDefinition[];
  layout?: string;
}>();

async function initCy(elements: cytoscape.ElementDefinition[] | undefined) {
  if (!cyRef.value || !elements) return;
  cyInstance.value?.destroy();
  console.time("Rendering Graph");

  const options: MyCytoscapeOptions = {
    container: cyRef.value,
    elements,
    renderer: {
      name: "canvas",
      webgl: true,
      showFps: false,
      webglDebug: false,
      webglTexSize: 8192,
      webglTexRows: 32,
      webglBatchSize: 4096,
      webglTexPerBatch: 32,
    },
    layout: { name: "preset" },
    zoom: 0.1,
    minZoom: 0.01,
    maxZoom: 3,
    hideEdgesOnViewport: true,
    hideLabelsOnViewport: true,
    textureOnViewport: true,
    zoomingEnabled: true,
    selectionType: "single",
    wheelSensitivity: 0.1,
    motionBlur: false, // Disabled for large graphs
    pixelRatio: 1,
  };

  cyInstance.value = cytoscape(options);

  // Apply styles immediately for better performance
  cyInstance.value.style(style as cytoscape.StylesheetJson);

  cyInstance.value?.ready(() => {
    console.timeEnd("Rendering Graph");
    (window as Window & { cy?: cytoscape.Core | null }).cy = cyInstance.value;

    // Determine parent origin safely (fallback to '*')
    let parentOrigin = "*";
    try {
      if (document.referrer) parentOrigin = new URL(document.referrer).origin;
    } catch (e) {
      console.warn(e);
    }

    // Announce ready to parent
    window.parent.postMessage({ type: "cy:ready" }, parentOrigin);
  });
}

onMounted(async () => {
  await nextTick();
  initCy(props.elements);
});

watch(
  () => props.elements,
  (newElements) => {
    initCy(newElements);
  }
);

defineExpose({ cy: cyInstance });
</script>

<template>
  <client-only>
    <div id="cyRef" ref="cyRef" class="w-full h-screen overflow-hidden" />
  </client-only>
</template>
