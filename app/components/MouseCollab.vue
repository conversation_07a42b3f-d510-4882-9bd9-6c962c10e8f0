<template>
  <div class="collab-area">
    <div
      v-for="(cursor, id) in collaborators"
      :key="id"
      class="cursor"
      :style="{ left: cursor.x + 'px', top: cursor.y + 'px' }"
    >
      🖱️
    </div>
  </div>
</template>

<script setup lang="ts">
import { throttle } from "lodash-es";
import { useMouse } from "@vueuse/core";
import { watch } from "vue";
import type { Socket } from "socket.io-client";

type CollaboratorsMap = Record<string, { x: number; y: number }>;

const nuxtApp = useNuxtApp();
const socket = nuxtApp.$socket as unknown as Socket;
const collaborators = nuxtApp.$collaborators as unknown as CollaboratorsMap;

const { x, y } = useMouse();

const emitMouseThrottled = throttle(() => {
  socket.emit("mouse-move", { x: x.value, y: y.value });
}, 30);

watch([x, y], () => {
  emitMouseThrottled();
});
</script>

<style>
.collab-area {
  width: 100%;
  height: 100vh;
  position: relative;
  background: #f0f0f0;
  overflow: hidden;
}
.cursor {
  position: absolute;
  pointer-events: none;
  transform: translate(-50%, -50%);
  font-size: 24px;
}
</style>
