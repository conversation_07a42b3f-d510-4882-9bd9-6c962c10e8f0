<template>
  <div class="flex flex-col min-h-screen bg-gray-50 text-black">
    <!-- Header -->
    <header class="bg-white shadow-md p-4 flex items-center justify-between">
      <h1 class="text-xl font-bold">Euler Analyst</h1>
      <nav class="space-x-4">
        <NuxtLink to="/" class="text-black hover:text-blue-600">
          home
        </NuxtLink>
      </nav>
    </header>

    <!-- Main Content -->
    <main class="flex-1 p-8">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-white shadow-inner p-4 text-center text-black">
      &copy; {{ new Date().getFullYear() }} Euler Analyst
    </footer>
  </div>
</template>

<script setup lang="ts">
// You can add composables here if needed in the layout
</script>

<style scoped>
/* Optional custom styles */
</style>
