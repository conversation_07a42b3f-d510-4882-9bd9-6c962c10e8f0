<template>
  <div class="min-h-screen flex items-center justify-center">
    <!-- Loader -->
    <div v-if="showLoader" class="loader" />

    <!-- Graph -->
    <CytoscapeGraph v-else :layout="layout" :elements="graph?.elements" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import type { ElementDefinition } from "cytoscape";
import { useAsyncData } from "nuxt/app";

definePageMeta({ layout: false });

const router = useRouter();
const layout = ref(
  (router.currentRoute.value.query.layout as string) || "grid"
);
const baseUrl = ref((router.currentRoute.value.query.baseUrl as string) || "");
const token = ref((router.currentRoute.value.query.token as string) || "");
const prefixURL = router.options.history.base;

const { data: graph, pending } = useAsyncData<{
  elements: ElementDefinition[];
}>(
  "graph-frame",
  () =>
    $fetch(`${prefixURL}/api/generate-graph`, {
      params: {
        layout: layout.value,
        baseUrl: baseUrl.value,
        token: token.value,
      },
    }),
  {
    watch: [() => layout.value],
    server: true,
    lazy: true,
    immediate: true,
  }
);

const hydrated = ref(false);
onMounted(() => {
  hydrated.value = true;
});

const showLoader = computed(() => pending.value || !hydrated.value);
</script>

<style scoped>
.loader {
  width: 48px;
  height: 48px;
  border: 1px solid #409eff;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}
@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
