<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Cytoscape Graph -->
    <section>
      <h2 class="text-xl font-semibold">Cytoscape Graph</h2>
      <div class="flex items-center space-x-4 mb-4">
        <label class="text-sm text-gray-700">Layout</label>
        <select v-model="selectedLayout" class="border px-2 py-1 rounded">
          <option value="preset">preset</option>
          <option value="grid">grid</option>
          <option value="circle">circle</option>
          <option value="concentric">concentric</option>
          <option value="breadthfirst">breadthfirst</option>
          <option value="cose">cose</option>
        </select>
      </div>
      <iframe
        id="graphFrame"
        ref="graphFrame"
        :src="`${baseURL}/graph-frame?layout=${selectedLayout}&baseUrl=${apiURL}&token=${token}`"
        width="100%"
        height="600"
        frameborder="0"
        style="background: white; border-radius: 8px"
      />
    </section>

    <!-- MapTiler Screenshot -->
    <section>
      <h2 class="text-xl font-semibold mb-2">Map Screenshot</h2>
      <div class="flex items-center space-x-4 mb-4">
        <input
          v-model="mapCenter"
          placeholder="lat,lng"
          class="border px-2 py-1 rounded"
        />
        <input
          v-model.number="mapZoom"
          type="number"
          placeholder="zoom"
          class="border px-2 py-1 rounded w-20"
        />
        <button
          class="bg-blue-600 text-white px-4 py-1 rounded hover:bg-blue-700"
          @click="captureMap"
        >
          Capture
        </button>
      </div>

      <div v-if="mapScreenshot" class="border rounded overflow-hidden">
        <img :src="mapScreenshot" alt="Map Screenshot" />
      </div>
    </section>

    <!-- Cytoscape Screenshot -->
    <section>
      <h2 class="text-xl font-semibold mb-2">Cytoscape Screenshot</h2>
      <button
        class="bg-green-600 text-white px-4 py-1 rounded hover:bg-green-700"
        @click="captureCytoscapeScreenshot"
      >
        Capture Cytoscape Screenshot
      </button>
      <div v-if="cyScreenshot" class="border rounded overflow-hidden mt-4">
        <img :src="cyScreenshot" alt="Cytoscape Screenshot" />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from "vue";
import { useRoute } from "vue-router";
const router = useRouter();
const baseURL = router.options.history.base;
const mapCenter = ref("48.8566,2.3522"); // Paris default
const mapZoom = ref(10);
const mapScreenshot = ref("");
const cyScreenshot = ref("");
// Minimal shape we rely on from Cytoscape core
type CytoscapeCoreLike = {
  json: () => { elements?: Record<string, unknown> };
  fit?: (eles?: unknown, padding?: number) => void;
};
// Direct handle to the iframe's Cytoscape instance when same-origin
const childCy = ref<CytoscapeCoreLike | null>(null);

const route = useRoute();
const apiURL = ref("/graph-workspace/32776/graph?parameters=ICON_DISPLAY_NAME");
const token = ref(
  "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJkb3J5IiwidmVydGljYWwiOiJkZXYxIiwibGFuZ3VhZ2UiOiJmciIsImlhdCI6MTc1NzkyMzQ5NSwiZXhwIjoxNzU4MDA5ODk1fQ.nepZAYKuZb-nNtVKPSXW0kE_9JbsL5rtZp-BHO5jf-4"
);
// Initialize selectedLayout from query or default to 'grid'
const selectedLayout = ref((route.query.layout as string) || "preset");

// Frame messaging
const graphFrame = ref<HTMLIFrameElement | null>(null);
const isChildReady = ref(false);
/**
 * Handles postMessage events from the Cytoscape iframe.
 * - On "cy:ready", sets up the childCy reference and fits the graph.
 * - On "cy:event", logs and can handle forwarded Cytoscape events.
 */
function onMessage(event: MessageEvent) {
  const data = event.data || {};
  console.log("[parent] message received", {
    origin: event.origin,
    data,
  });

  if (data.type === "cy:ready") {
    isChildReady.value = true;

    // Try to grab direct reference to cy from the iframe (same-origin)
    try {
      const win = graphFrame.value?.contentWindow as
        | (Window & { cy?: unknown })
        | undefined;
      childCy.value = (win?.cy ?? null) as CytoscapeCoreLike | null;
      if (childCy.value) {
        console.info(
          "[parent] Cytoscape instance reference acquired from iframe."
        );
      } else {
        console.warn("[parent] Cytoscape instance not found on iframe window.");
      }
    } catch (err) {
      childCy.value = null;
      console.warn(
        "[parent] Could not access Cytoscape instance from iframe:",
        err
      );
    }

    // Fit using direct cy reference if available
    try {
      childCy.value?.fit?.();
      console.info("[parent] Called fit() on Cytoscape instance.");
    } catch (err) {
      console.warn("[parent] Could not fit Cytoscape instance:", err);
    }
  } else if (data.type === "cy:event") {
    // handle forwarded events if needed
    console.debug("[parent] cy event", data);
  } else {
    // Unknown message type
    console.warn(
      "[parent] Unknown message type received from iframe:",
      data.type,
      data
    );
  }
}

onMounted(() => {
  window.addEventListener("message", onMessage);
});

onBeforeUnmount(() => {
  window.removeEventListener("message", onMessage);
});

async function captureMap() {
  try {
    const query = new URLSearchParams({
      center: mapCenter.value,
      zoom: mapZoom.value.toString(),
    });
    const shapes = `&shapes={"type":"FeatureCollection","features":[{"type":"Feature","geometry":{"type":"Point","coordinates":[2.3522,48.8566]}},{"type":"Feature","geometry":{"type":"LineString","coordinates":[[2.35,48.85],[2.36,48.86]]}},{"type":"Feature","geometry":{"type":"Polygon","coordinates":[[[2.34,48.85],[2.36,48.85],[2.36,48.87],[2.34,48.87],[2.34,48.85]]]}}]}`;
    const response = await fetch(
      `${baseURL}/api/map-screenshot?${query.toString()}${shapes}`
    );
    mapScreenshot.value = await response.text();
  } catch (err) {
    console.error("Map screenshot failed", err);
  }
}

async function captureCytoscapeScreenshot() {
  try {
    if (!childCy.value || typeof childCy.value.json !== "function") {
      throw new Error("Cytoscape is not ready yet");
    }
    const graphData = childCy.value.json();
    const response = await fetch(`${baseURL}/api/graph-screenshots`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        ...(graphData.elements ?? {}),
        width: 1800,
        height: 500,
      }),
    });
    const data = await response.json();
    // Prefer base64 image if available, else use file path
    if (data.image) {
      cyScreenshot.value = `data:image/png;base64,${data.image}`;
    } else {
      cyScreenshot.value = data.path;
    }
  } catch (err) {
    console.error("Cytoscape screenshot failed", err);
  }
}
</script>

<style scoped>
/* Optional: add custom styles */
</style>
