import { defineNuxtPlugin, useRuntimeConfig } from "nuxt/app";
import type { NuxtApp } from "nuxt/app";
import { io, type Socket } from "socket.io-client";
import { reactive } from "vue";

export default defineNuxtPlugin((nuxtApp: NuxtApp) => {
  const config = useRuntimeConfig();
  const url = config.public?.socketUrl || "http://localhost:3002";
  const socket: Socket = io(url, {
    transports: ["websocket", "polling"],
    autoConnect: true,
  });

  const collaborators = reactive<Record<string, { x: number; y: number }>>({});

  socket.on("connect", () => console.log("✅ Socket connected"));
  socket.on("connect_error", (err) =>
    console.error("⚠️ socket connect_error:", err)
  );
  socket.on("error", (err) => console.error("⚠️ socket error:", err));
  socket.on("reconnect", (attempt) =>
    console.log("ℹ️ socket reconnected, attempt:", attempt)
  );
  socket.on("reconnect_attempt", (attempt) =>
    console.log("ℹ️ socket reconnect_attempt:", attempt)
  );
  socket.on("reconnect_error", (err) =>
    console.error("⚠️ socket reconnect_error:", err)
  );
  socket.on("reconnect_failed", () =>
    console.error("⚠️ socket reconnect_failed")
  );

  socket.on("init-cursors", (users) => Object.assign(collaborators, users));
  socket.on(
    "mouse-move",
    (data) => (collaborators[data.id] = { x: data.x, y: data.y })
  );
  socket.on("remove-cursor", (id) => {
    Reflect.deleteProperty(collaborators, id);
  });
  if (typeof window !== "undefined") {
    // Expose for quick debugging in the browser console
    // @ts-expect-error attach debug handle
    window.tsocket = socket;
  }
  // Provide under lowercase keys; these will be available as $socket, $collaborators
  nuxtApp.provide("socket", socket);
  nuxtApp.provide("collaborators", collaborators);
});
