[{"selector": ".hidden", "style": {"visibility": "hidden", "background-image": "null", "label": ""}}, {"selector": ".nodeGroup", "style": {"width": "data(width)", "height": "data(height)", "background-color": "data(color)", "background-image": "data(backgroundImage)", "background-fit": "cover", "background-repeat": "no-repeat", "label": "data(shownValue)", "text-halign": "center", "shape": "round-rectangle", "text-valign": "bottom", "text-opacity": "1", "font-family": "<PERSON><PERSON>", "text-overflow-wrap": "anywhere", "text-background-color": "rgb(250,250,250)", "text-background-opacity": "1", "text-background-shape": "roundrectangle", "border-width": "3px", "font-size": "11px", "border-style": "solid", "border-color": "rgb(255,255,255)", "text-margin-y": "15px", "opacity": "1", "background-height-relative-to": "inner", "background-width-relative-to": "inner"}}, {"selector": ".nodes", "style": {"width": "data(width)", "height": "data(height)", "padding": "5px", "background-color": "data(color)", "background-image": "data(backgroundImage)", "background-fit": "cover", "background-repeat": "no-repeat", "label": "data(shownValue)", "text-halign": "center", "text-valign": "bottom", "text-opacity": "1", "font-family": "<PERSON><PERSON>", "text-background-color": "rgb(250,250,250)", "text-background-opacity": "1", "text-background-shape": "roundrectangle", "text-wrap": "ellipsis", "text-max-width": "150px", "font-size": "11px", "min-zoomed-font-size": "10px", "text-overflow-wrap": "anywhere", "border-width": "3px", "border-style": "solid", "border-color": "rgb(255,255,255)", "text-margin-y": "15px", "opacity": "1", "background-height-relative-to": "inner", "background-width-relative-to": "inner"}}, {"selector": ".newNode", "style": {"width": "data(width)", "height": "data(height)", "padding": "5px", "background-color": "data(color)", "background-image": "data(backgroundImage)", "background-fit": "cover", "background-repeat": "no-repeat", "label": "data(shownValue)", "text-halign": "center", "text-valign": "bottom", "text-opacity": "1", "font-family": "<PERSON><PERSON>", "shape": "cut-rectangle", "text-background-color": "rgb(250,250,250)", "text-background-opacity": "1", "text-background-shape": "roundrectangle", "text-wrap": "ellipsis", "text-max-width": "150px", "font-size": "11px", "min-zoomed-font-size": "10px", "text-overflow-wrap": "anywhere", "border-width": "3px", "border-style": "solid", "border-color": "rgb(255,255,255)", "text-margin-y": "15px", "opacity": "1", "background-height-relative-to": "inner", "background-width-relative-to": "inner"}}, {"selector": "edge", "style": {"label": "data(label)", "text-opacity": "1", "font-family": "<PERSON><PERSON>", "control-point-step-size": "40px", "text-wrap": "wrap", "text-max-width": "100px", "text-justification": "auto", "text-overflow-wrap": "anywhere", "text-background-color": "rgb(250,250,250)", "text-background-padding": "5px", "text-background-opacity": "1", "text-background-shape": "roundrectangle", "width": "data(width)", "text-rotation": "autorotate", "curve-style": "bezier", "min-zoomed-font-size": "10px", "font-size": "11px", "source-arrow-shape": "none", "target-arrow-shape": "triangle", "target-endpoint": "outside-to-node", "line-color": "data(color)", "line-style": "data(style)", "control-point-distances": "50px", "target-arrow-color": "data(color)", "arrow-scale": "1"}}, {"selector": ".newEdge", "style": {"label": "data(label)", "text-opacity": "1", "font-family": "<PERSON><PERSON>", "control-point-step-size": "40px", "text-wrap": "wrap", "text-max-width": "100px", "text-justification": "auto", "text-overflow-wrap": "anywhere", "text-background-color": "rgb(250,250,250)", "text-background-padding": "5px", "text-background-opacity": "1", "text-background-shape": "roundrectangle", "width": "1px", "text-rotation": "autorotate", "curve-style": "bezier", "min-zoomed-font-size": "10px", "font-size": "11px", "source-arrow-shape": "none", "target-arrow-shape": "triangle", "target-endpoint": "outside-to-node", "line-color": "data(color)", "line-style": "dashed", "target-arrow-color": "data(color)", "arrow-scale": "1"}}, {"selector": ".similarity<PERSON>dge", "style": {"label": "data(label)", "text-opacity": "1", "font-family": "<PERSON><PERSON>", "control-point-step-size": "40px", "text-wrap": "wrap", "text-max-width": "100px", "text-justification": "auto", "text-overflow-wrap": "anywhere", "text-background-color": "rgb(250,250,250)", "text-background-padding": "5px", "text-background-opacity": "1", "text-background-shape": "roundrectangle", "width": "1px", "text-rotation": "autorotate", "curve-style": "bezier", "min-zoomed-font-size": "10px", "font-size": "11px", "source-arrow-shape": "none", "target-arrow-shape": "none", "target-endpoint": "outside-to-node", "line-color": "data(color)", "line-style": "dashed", "target-arrow-color": "data(color)", "arrow-scale": "1"}}, {"selector": ".edgeTransition", "style": {"transition-property": "line-color target-arrow-color width text-background-color text-background-padding", "transition-duration": "0.2s", "transition-delay": "50ms", "transition-timing-function": "ease-in-out-sine"}}, {"selector": "node:selected", "style": {"border-width": "4px", "border-color": "data(colorOnSelect)", "z-compound-depth": "top"}}, {"selector": "edge:selected", "style": {"line-color": "data(colorOnSelect)", "target-arrow-color": "data(colorOnSelect)", "width": "data(widthOnSelect)", "z-compound-depth": "top"}}, {"selector": "edge:selected, node:selected", "style": {"text-background-color": "rgb(255,255,255)", "text-background-padding": "2px"}}, {"selector": "core", "style": {"active-bg-color": "rgb(0,0,0)", "active-bg-opacity": "0", "active-bg-size": "0px"}}, {"selector": ".cdnd-grabbed-node", "style": {}}, {"selector": ".cdnd-drop-sibling", "style": {}}, {"selector": "node:parent", "style": {"background-color": "rgb(255,255,255)", "border-color": "rgb(9,96,142)", "border-opacity": "0.5", "padding": "15px", "text-wrap": "ellipsis", "text-max-width": "150px", "font-size": "11px", "min-zoomed-font-size": "10px", "text-overflow-wrap": "anywhere", "z-compound-depth": "bottom"}}, {"selector": ".cdnd-drop-target", "style": {"border-color": "rgb(255,0,0)", "border-opacity": "0.5", "border-style": "dashed"}}, {"selector": ".nodeCommunity", "style": {"background-color": "data(color)", "shape": "ellipse", "padding": "20px", "border-width": "2px", "border-color": "data(color)", "background-opacity": "0.25", "label": "data(entityLabel)", "text-halign": "center", "text-valign": "bottom", "text-opacity": "1", "font-family": "<PERSON><PERSON>", "text-background-color": "rgb(250,250,250)", "text-background-opacity": "1", "text-background-shape": "roundrectangle", "text-wrap": "ellipsis", "text-max-width": "150px", "font-size": "11px", "min-zoomed-font-size": "10px", "text-overflow-wrap": "anywhere", "border-style": "solid", "text-margin-y": "15px", "opacity": "1", "background-height-relative-to": "inner", "background-width-relative-to": "inner"}}]