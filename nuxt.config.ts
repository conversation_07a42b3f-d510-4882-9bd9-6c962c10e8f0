import { defineNuxtConfig } from "nuxt/config";

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  ssr: true,
  typescript: { strict: true },
  compatibilityDate: "2025-07-15",

  devtools: { enabled: true },
  devServer: {
    host: "0.0.0.0",
    port: 3001,
  },
  modules: [
    "@nuxt/eslint",
    "@nuxt/image",
    "@nuxtjs/tailwindcss",
    "@element-plus/nuxt",
    "@nuxt/test-utils/module",
    "@vueuse/nuxt",
    [
      "nuxt-delay-hydration",
      {
        debug: process.env.NODE_ENV === "development",
        mode: "init",
      },
    ],
  ],

  build: {
    transpile: ["cytoscape"],
  },

  vite: {
    server: {
      watch: {
        usePolling: true,
        interval: 100,
        ignored: [
          "**/node_modules/**",
          "**/.git/**",
          "**/.nuxt/**",
          "**/dist/**",
        ],
      },
    },
  },

  runtimeConfig: {
    sentryDsn: "",
    mapTilerKey: "IXlymMR3FbMT3sMuPSkX",
    eapUrl: "https://eap.eulerds.cloud",
    grapheneServiceUrl: "/graphene-service/api",
    public: {
      socketUrl: process.env.SOCKET_URL || "http://localhost:3002",
    },
  },
  nitro: {
    preset: "node-server",
    experimental: {
      asyncContext: true,
      websocket: true,
    },
    routeRules: {
      // Public shell (no token) - cache at edge
      "/ssr-analyst/graph-frame": {
        headers: {
          "cache-control": "public, s-maxage=60, stale-while-revalidate=300",
        },
      },
      // Any tokenized query → no-store
      "/ssr-analyst/graph-frame**": {
        headers: { "cache-control": "private, no-store" },
      },
    },
  },
  experimental: {
    renderJsonPayloads: true, // reduces HTML size by moving payload to JSON
  },

  app: {
    baseURL: "/ssr-analyst/",
  },

  sourcemap: {
    client: false,
    server: false,
  },
});
