{"name": "nuxt-app", "type": "module", "private": true, "engines": {"node": ">=22.0.0"}, "overrides": {"glob": "^10.3.0"}, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test": "vitest", "type-check": "tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@nuxt/eslint": "^1.9.0", "@nuxt/image": "^1.11.0", "@nuxt/vite-builder": "^4.0.3", "@nuxtjs/i18n": "^10.0.6", "@nuxtjs/tailwindcss": "^6.14.0", "@sentry/node": "^10.5.0", "@sentry/nuxt": "^10.5.0", "@sentry/vite-plugin": "^4.1.1", "@vueuse/core": "^13.9.0", "cytoscape": "^3.33.1", "cytoscape-animate-flow": "^0.0.2", "cytoscape-cise": "^2.0.1", "cytoscape-cola": "^2.5.1", "cytoscape-cose-bilkent": "^4.1.0", "cytoscape-elk": "^2.3.0", "cytoscape-euler": "^1.2.3", "cytoscape-node-html-label": "^1.2.2", "cytoscape-undo-redo": "^1.3.3", "pino": "^9.9.1", "pino-pretty": "^13.1.1", "puppeteer": "^24.17.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "vite": "^7.1.3", "vue": "^3.5.20", "vue-i18n": "^11.1.11", "vue-router": "^4.5.1"}, "devDependencies": {"@element-plus/nuxt": "^1.1.4", "@nuxt/test-utils": "^3.19.2", "@vitejs/plugin-vue": "^6.0.1", "@vue/test-utils": "^2.4.6", "@vueuse/nuxt": "^13.9.0", "autoprefixer": "^10.4.21", "element-plus": "^2.11.1", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "nuxt": "^4.0.3", "nuxt-delay-hydration": "^1.3.8", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}