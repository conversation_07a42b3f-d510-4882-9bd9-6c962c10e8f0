// import * as Sentry from "@sentry/vue";
// import { BrowserTracing } from "@sentry/tracing";
// import { defineNuxtPlugin } from "#app";

// export default defineNuxtPlugin((nuxtApp) => {
//   Sentry.init({
//     app: nuxtApp.vueApp,
//     dsn: nuxtApp.$config.public.NUXT_PUBLIC_SENTRY_DSN,
//     integrations: [new BrowserTracing()],
//     tracesSampleRate: 1.0,
//   });

//   return {
//     provide: { sentry: Sentry },
//   };
// });
