import { computeFcoseLayout } from "../utils/computeFcoseLayout";
import type { CytoscapeElements } from "types/common";

import { defineEventHandler, getValidatedQuery, createError } from "h3";
import cytoscape, { type LayoutOptions } from "cytoscape";
import { getCytoscapeLayoutByName } from "../utils/layoutUtils";
import { z } from "zod";
import logger from "../utils/logger";

// ----------------------
// Validation Schema
// ----------------------
const querySchema = z.object({
  layout: z.string().min(1).max(100),
  baseUrl: z.string().min(1),
  token: z.string().min(1),
});

// ----------------------
// <PERSON> Handler
// ----------------------
export default defineEventHandler(async (event) => {
  try {
    const { layout, baseUrl, token } = await getValidatedQuery(
      event,
      querySchema.parse
    );
    logger.info({ layout }, "layout");
    const result = await generateGraphCyJsonWithoutPuppeteer(
      layout,
      baseUrl,
      token
    );
    return result;
  } catch (err) {
    logger.error({ err }, "[/api/generate-graph] Error");
    // Sentry.captureException(err);
    throw createError({
      statusCode: 500,
      message: "Failed to generate graph " + err,
    });
  }
});
async function generateGraphCyJsonWithoutPuppeteer(
  layoutName: string,
  baseUrl: string,
  token: string
): Promise<CytoscapeElements> {
  const runtimeConfig = useRuntimeConfig();
  const url = `${runtimeConfig.eapUrl}${runtimeConfig.grapheneServiceUrl}${baseUrl}`;

  let graph: CytoscapeElements["elements"];
  try {
    const res = await fetch(url, {
      headers: { accept: "*/*", Authorization: `Bearer ${token}` },
    });
    if (!res.ok) {
      const text = await res.text().catch(() => "<could not read body>");
      logger.error({ status: res.status, body: text }, "Failed request");
      throw new Error(`Failed to fetch graph: ${res.status} ${res.statusText}`);
    }
    graph = await res.json();
  } catch (e) {
    throw new Error(`Could not fetch/parse remote graph: ${e}`);
  }
  if (layoutName === "cose_bilkent") {
    const data = await computeFcoseLayout(graph);

    return data;
  }
  const cy = cytoscape({
    elements: graph,
    headless: true,
    styleEnabled: false,
    layout: getCytoscapeLayoutByName(layoutName) as LayoutOptions,
  });

  return cy.json() as CytoscapeElements;
}

// ----------------------
// Puppeteer Fallback (optional heavy render)
// ----------------------
// async function generateGraphCyJson(
//   elements: CytoscapeElement[],
//   style: unknown[] = [],
//   layout: LayoutOptions = { name: "cose" }
// ): Promise<CytoscapeElements> {
//   const width = 800;
//   const height = 600;

//   try {
//     const data = {
//       nodes: elements.filter((e) => e.group === "nodes"),
//       edges: elements.filter((e) => e.group === "edges"),
//     };

//     const html = `
//       <html>
//         <head>
//           <meta charset='utf-8'/>
//           <script src='https://unpkg.com/cytoscape/dist/cytoscape.min.js'></script>
//         </head>
//         <body>
//           <div id='cyContainer' style='width:${width}px; height:${height}px;'></div>
//           <script>
//             document.addEventListener('DOMContentLoaded', () => {
//               window.cy = cytoscape({
//                 container: document.getElementById('cyContainer'),
//                 elements: ${JSON.stringify(data)},
//                 style: ${JSON.stringify(style)},
//                 layout: ${JSON.stringify(layout)}
//               });
//               window.cy.ready(() => {
//                 window.cyJson = window.cy.json();
//               });
//             });
//           </script>
//         </body>
//       </html>
//     `;

//     const browser = await getBrowser();
//     const page = await browser.newPage();
//     await page.setViewport({ width, height });
//     await page.setContent(html, { waitUntil: "networkidle0" });
//     await page.waitForSelector("#cyContainer");
//     await page.waitForFunction("window.cyJson !== undefined");

//     const cyJson = await page.evaluate(() => {
//       // @ts-expect-error - cyJson is defined in the script
//       return window.cyJson;
//     });
//     await page.close();

//     return cyJson as CytoscapeElements;
//   } catch (err) {
//     console.error("[generateGraphCyJson] Puppeteer failed:", err);
//     Sentry.captureException(err);
//     return { elements: [] };
//   }
// }
