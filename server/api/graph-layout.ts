import { readFileSync } from "fs";
import path from "path";
import { defineE<PERSON><PERSON><PERSON><PERSON>, readBody } from "h3";
import type { H3Event } from "h3";
import { getBrowser } from "../utils/browser";
import { Sentry } from "../utils/sentry";

/**
 * POST /api/graph-layout
 * Body: { graph?: { elements, style?, layout? }, layout?: any }
 *
 * Returns: { elements, layout: { name: 'preset' } }
 * - Elements will include absolute positions for nodes so client can render instantly.
 */
type Position = { x: number; y: number };
type NodePayload = { data: Record<string, unknown>; position: Position; group: "nodes" };
type EdgePayload = { data: Record<string, unknown>; group: "edges" };
type ElementsPayload = { nodes: NodePayload[]; edges: EdgePayload[] };

type GraphBody = {
  graph?: {
    elements?: unknown;
    style?: unknown;
    layout?: Record<string, unknown>;
  };
  layout?: Record<string, unknown>;
};

export default defineEventHandler(async (event: H3Event) => {
  try {
    const body = (await readBody(event).catch(() => ({}))) as GraphBody;

    // Fallback to bundled assets if no body provided
    let elements: unknown;
    let style: unknown | undefined = undefined;
    let layout: Record<string, unknown> | undefined =
      body?.layout || body?.graph?.layout || { name: "cose" };

    if (body?.graph?.elements) {
      elements = body.graph.elements;
      if (body.graph.style) style = body.graph.style;
    } else {
      const graphPath = path.resolve(process.cwd(), "assets/graph.json");
      const graph = JSON.parse(readFileSync(graphPath, "utf-8"));
      elements = graph.elements;
      style = graph.style || undefined;
      layout = graph.layout || layout;
    }

    // Load default style if still undefined
    if (!style) {
      const stylePath = path.resolve(
        process.cwd(),
        "assets/cytoscape-style.json"
      );
      try {
        style = JSON.parse(readFileSync(stylePath, "utf-8"));
      } catch {
        style = [];
      }
    }

    const browser = await getBrowser();
    const page = await browser.newPage();
    try {
      // Build a minimal HTML to compute layout and return node positions
      const html = `
        <html>
          <head>
            <meta charset='utf-8'/>
            <script src='https://unpkg.com/cytoscape/dist/cytoscape.min.js'></script>
          </head>
          <body>
            <div id='cy' style='width:800px;height:600px;'></div>
            <script>
              function computeLayout(elements, style, layout) {
                const cy = cytoscape({
                  container: document.getElementById('cy'),
                  elements: elements,
                  style: style,
                  layout: layout || { name: 'cose' }
                });
                window.cy = cy;
                return new Promise((resolve) => {
                  const l = cy.layout(layout || { name: 'cose' });
                  let resolved = false;
                  const finish = () => {
                    if (resolved) return;
                    resolved = true;
                    const nodes = cy.nodes().map(n => ({
                      data: n.data(),
                      position: n.position(),
                      group: 'nodes'
                    }));
                    const edges = cy.edges().map(e => ({
                      data: e.data(),
                      group: 'edges'
                    }));
                    resolve({ nodes, edges });
                  };
                  // bind before run to avoid missing the event
                  l.on('layoutstop', finish);
                  // Fallback in case event doesn't fire
                  setTimeout(finish, 15000);
                  l.run();
                });
              }
              window.__computeLayout = computeLayout;
            </script>
          </body>
        </html>
      `;

      await page.setContent(html, { waitUntil: "networkidle0" });
      await page.waitForSelector("#cy");
      // Ensure Cytoscape is available
      await page.waitForFunction("window.cytoscape !== undefined", {
        timeout: 30000,
      });
      // Compute layout in page context and store result on window
      await page.evaluate(
        (els, sty, lay) => {
          const win = window as unknown as {
            __layoutDone?: boolean;
            __layoutResult?: unknown;
            __computeLayout?: (e: unknown, s: unknown, l: unknown) => Promise<unknown>;
            cy?: unknown;
          };
          win.__layoutDone = false;
          (win.__computeLayout?.(els, sty, lay) ?? Promise.resolve(null))
            .then((res: unknown) => {
              win.__layoutResult = res ?? null;
              win.__layoutDone = true;
            })
            .catch(() => {
              try {
                const cy = win.cy;
                const cyObj = cy as {
                  nodes: () => Array<{
                    data: () => Record<string, unknown>;
                    position: () => Position;
                  }>;
                  edges: () => Array<{
                    data: () => Record<string, unknown>;
                  }>;
                };
                const nodes = cy
                  ? cyObj.nodes().map((n) => ({
                      data: n.data(),
                      position: n.position(),
                      group: "nodes" as const,
                    }))
                  : [];
                const edges = cy
                  ? cyObj.edges().map((e) => ({ data: e.data(), group: "edges" as const }))
                  : [];
                win.__layoutResult = { nodes, edges };
              } catch {
                win.__layoutResult = { nodes: [], edges: [] };
              }
              win.__layoutDone = true;
            });
        },
        elements,
        style,
        layout
      );
      await page.waitForFunction("window.__layoutDone === true", {
        timeout: 30000,
      });
      const layoutResult = (await page.evaluate(() => {
        const win = window as unknown as { __layoutResult?: ElementsPayload };
        return (win.__layoutResult as ElementsPayload) ?? { nodes: [], edges: [] };
      })) as ElementsPayload;

      const presetElements = [
        ...layoutResult.nodes.map((n: NodePayload) => ({
          data: n.data,
          position: n.position,
          group: "nodes" as const,
        })),
        ...layoutResult.edges.map((e: EdgePayload) => ({
          data: e.data,
          group: "edges" as const,
        })),
      ];

      return {
        elements: presetElements,
        layout: { name: "preset" },
        style,
      };
    } finally {
      await page.close();
    }
  } catch (err) {
    Sentry.captureException(err);
    throw err;
  }
});
