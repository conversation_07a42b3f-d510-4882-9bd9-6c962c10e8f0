import { Sentry } from "../utils/sentry";
import { readFileSync } from "fs";
import path from "path";
import { getCytoscapeImage } from "../utils/cytosnap";
import { defineEventHandler, readBody } from "h3";

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    let elements, style, layout;

    // Load shared style
    const stylePath = path.resolve(
      process.cwd(),
      "assets/cytoscape-style.json"
    );
    style = JSON.parse(readFileSync(stylePath, "utf-8"));

    if (body.graph) {
      elements = body.graph.elements;
      if (body.graph.style) style = body.graph.style;
      if (body.graph.layout) layout = body.graph.layout;
    } else {
      elements = {
        nodes: body.nodes || [],
        edges: body.edges || [],
      };
      if (body.style) style = body.style;
      if (body.layout) layout = body.layout;
    }
    const width = body.width || 800;
    const height = body.height || 600;
    const background = body.background || "#fff";
    const format = body.format || "png";
    const resolvesTo = body.resolvesTo || "base64";

    const image = await getCytoscapeImage({
      elements,
      style,
      layout,
      width,
      height,
      background,
      format,
      resolvesTo,
    });
    return { image };
  } catch (err) {
    Sentry.captureException(err);
    throw err;
  }
});
