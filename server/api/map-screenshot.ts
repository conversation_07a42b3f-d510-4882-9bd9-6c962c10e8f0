import { getBrowser } from "../utils/browser";
import { Sentry } from "../utils/sentry";
import { defineEvent<PERSON>and<PERSON>, getQuery } from "h3";

export default defineEventHandler(async (event) => {
  const query = getQuery(event);
  const center =
    typeof query.center === "string" ? query.center : "48.8566,2.3522"; // default Paris
  const zoom = typeof query.zoom === "string" ? query.zoom : "10";
  const runtimeConfig = useRuntimeConfig();
  const mapTilerKey = runtimeConfig.mapTilerKey;

  if (!mapTilerKey) {
    throw new Error("MAPTILER_KEY environment variable is not set.");
  }

  // Parse shapes from query (expecting JSON string)
  let shapesGeoJson = null;
  if (typeof query.shapes === "string") {
    try {
      shapesGeoJson = JSON.parse(query.shapes);
    } catch (err) {
      Sentry.captureException(err);
      throw new Error("Invalid shapes parameter: must be valid JSON");
    }
  }

  try {
    const browser = await getBrowser();
    const page = await browser.newPage();
    await page.setViewport({ width: 1024, height: 768 });
    // Inject shapes as a JS variable if present
    const shapesScript = shapesGeoJson
      ? `<script>window.shapes = ${JSON.stringify(shapesGeoJson)};</script>`
      : "";

    // Navigate to a minimal HTML that loads MapTiler and waits for map to be fully rendered
    await page.setContent(`
      <div id="map" style="width:1024px;height:768px"></div>
      <link href="https://api.maptiler.com/maps/streets/style.css?key=${mapTilerKey}" rel="stylesheet">
      <script src="https://unpkg.com/maplibre-gl@3.2.1/dist/maplibre-gl.js"></script>
      ${shapesScript}
      <script>
        window.mapLoaded = false;
        function initMap() {
          const map = new maplibregl.Map({
            container: 'map',
            style: 'https://api.maptiler.com/maps/streets/style.json?key=${mapTilerKey}',
            center: [${center.split(",")[1]}, ${center.split(",")[0]}],
            zoom: ${zoom}
          });
          map.on('idle', () => { window.mapLoaded = true; });
          map.on('load', () => {
            // Add shapes if provided
            if (window.shapes) {
              map.addSource('shapes', {
                type: 'geojson',
                data: window.shapes
              });
              // Points
              map.addLayer({
                id: 'points',
                type: 'circle',
                source: 'shapes',
                filter: ['==', '$type', 'Point'],
                paint: { 'circle-radius': 8, 'circle-color': '#007cbf' }
              });
              // Lines
              map.addLayer({
                id: 'lines',
                type: 'line',
                source: 'shapes',
                filter: ['==', '$type', 'LineString'],
                paint: { 'line-width': 4, 'line-color': '#ff0000' }
              });
              // Polygons
              map.addLayer({
                id: 'polygons',
                type: 'fill',
                source: 'shapes',
                filter: ['==', '$type', 'Polygon'],
                paint: { 'fill-color': '#00ff00', 'fill-opacity': 0.5 }
              });
            }
          });
        }
        if (window.maplibregl) {
          initMap();
        } else {
          var script = document.createElement('script');
          script.src = 'https://unpkg.com/maplibre-gl@3.2.1/dist/maplibre-gl.js';
          script.onload = initMap;
          document.head.appendChild(script);
        }
      </script>
    `);

    // Wait for the map to finish rendering (idle event, up to 10 seconds)
    await page.waitForFunction("window.mapLoaded === true", { timeout: 10000 });
    const base64 = await page.screenshot({ type: "png", encoding: "base64" });
    await page.close(); // Only close the page, not the browser

    return `data:image/png;base64,${base64}`;
  } catch (err) {
    Sentry.captureException(err);
    throw err;
  }
});
