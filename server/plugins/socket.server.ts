import { createServer } from "node:http";
import { Server } from "socket.io";
import logger from "../utils/logger";

export default defineNitroPlugin((nitroApp) => {
  const globals = globalThis as unknown as {
    __ioServer__?: Server;
    __ioHttpServer__?: import("node:http").Server;
  };

  if (globals.__ioServer__) {
    logger.info("Socket.IO already initialized; skipping re-init");
    return;
  }

  const httpServer = createServer();
  const io = new Server(httpServer, {
    cors: {
      origin: "*", // 🔒 tighten this in production!
      methods: ["GET", "POST"],
    },
  });

  const users: Record<string, { x: number; y: number }> = {};

  io.on("connection", (socket) => {
    logger.info({ socketId: socket.id }, "Socket connected");
    console.log("✅ Client connected:", socket.id);

    socket.emit("init-cursors", users);

    socket.on("mouse-move", (data: { x: number; y: number }) => {
      users[socket.id] = data;
      socket.broadcast.emit("mouse-move", { id: socket.id, ...data });
    });

    socket.on("pingFromClient", (msg) => {
      console.log("Received:", msg);
      socket.emit("pongFromServer", { msg: "Hello from Nuxt server" });
    });

    socket.on("disconnect", () => {
      Reflect.deleteProperty(users, socket.id);
      io.emit("remove-cursor", socket.id);
      console.log("❌ Client disconnected:", socket.id);
    });
  });

  httpServer.on("error", (err: NodeJS.ErrnoException) => {
    if (err && err.code === "EADDRINUSE") {
      logger.warn("Port 3001 already in use; skipping Socket.IO listen");
    } else {
      logger.error({ err }, "Socket.IO HTTP server error");
    }
  });

  httpServer.listen(3002, () => {
    console.log("📡 Socket.IO server listening on http://localhost:3002");
  });

  globals.__ioServer__ = io;
  globals.__ioHttpServer__ = httpServer;

  nitroApp.hooks.hook("close", () => {
    if (globals.__ioServer__) {
      try {
        globals.__ioServer__.close();
      } catch (closeErr) {
        logger.warn({ closeErr }, "Failed to close Socket.IO server");
      }
    }
    if (globals.__ioHttpServer__) {
      try {
        globals.__ioHttpServer__.close();
      } catch (closeErr) {
        logger.warn({ closeErr }, "Failed to close Socket.IO HTTP server");
      }
    }
    Reflect.deleteProperty(globals, "__ioServer__");
    Reflect.deleteProperty(globals, "__ioHttpServer__");
  });
});
