import type { <PERSON><PERSON><PERSON> } from "puppeteer";
import puppeteer from "puppeteer";

let _browser: Browser | null = null;

export async function getBrowser(): Promise<Browser> {
  if (!_browser) {
    _browser = await puppeteer.launch({ args: ["--no-sandbox"] });
  }
  return _browser;
}
export async function closeBrowser() {
  if (_browser) {
    await _browser.close();
    _browser = null;
  }
}
