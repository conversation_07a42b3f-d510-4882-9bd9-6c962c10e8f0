import { spawn } from "node:child_process";
import path from "node:path";
import type { CytoscapeElements } from "types/common";

export type PositionedNode = {
  id: string;
  position: { x: number; y: number };
};

export async function computeFcoseLayout(
  graph: CytoscapeElements["elements"]
): Promise<CytoscapeElements> {
  const workerPath = path.resolve(
    process.cwd(),
    "server/workers/fcose-runner.mjs"
  );
  const payload = JSON.stringify({ nodes: graph.nodes, edges: graph.edges });

  const result = await new Promise<string>((resolve, reject) => {
    const child = spawn(process.execPath, [workerPath], {
      stdio: ["pipe", "pipe", "pipe"],
      env: { ...process.env },
    });

    let stdout = "";
    let stderr = "";

    child.stdout.setEncoding("utf-8");
    child.stderr.setEncoding("utf-8");

    child.stdout.on("data", (d) => (stdout += d));
    child.stderr.on("data", (d) => {
      stderr += d;
      console.warn("[fcose-runner stderr]", d.toString());
    });

    const timeout = setTimeout(() => {
      child.kill();
      reject(new Error("fcose-runner timed out"));
    }, 60000);

    child.on("error", reject);
    child.on("close", (code) => {
      clearTimeout(timeout);
      if (code === 0) return resolve(stdout);
      reject(new Error(`fcose-runner exited with code ${code}: ${stderr}`));
    });

    child.stdin.write(payload);
    child.stdin.end();
  });

  try {
    const parsed = JSON.parse(result) as { nodes: PositionedNode[] };
    const positions = parsed.nodes ?? [];

    const positionById = new Map<string, { x: number; y: number }>();
    positions.forEach((p) => positionById.set(p.id, p.position));

    const positionedNodes = graph.nodes.map((el) => {
      const id = el.data?.id ?? el.data?.name;
      if (!id) {
        console.warn("Node without id/name, skipping position", el);
        return el;
      }
      const pos = positionById.get(String(id));
      return pos ? { ...el, position: pos } : el;
    });

    return {
      elements: {
        nodes: positionedNodes,
        edges: graph.edges,
      },
    };
  } catch (e) {
    throw new Error(`Failed to parse fcose-runner output: ${e}`);
  }
}
