import type {
  ElementDefinition,
  LayoutOptions,
  StylesheetJson,
} from "cytoscape";
import { getBrowser } from "./browser";

export interface CytosnapOptions {
  elements: ElementDefinition[];
  style: StylesheetJson;
  layout?: LayoutOptions;
  width?: number;
  height?: number;
  background?: string;
  format?: "png" | "jpeg";
  resolvesTo?: "base64" | "base64uri";
}

export async function getCytoscapeImage({
  elements,
  style,
  layout = { name: "cose" },
  width = 800,
  height = 600,
  background = "#fff",
  format = "png",
  resolvesTo = "base64",
}: CytosnapOptions): Promise<string> {
  const html = `
    <html>
      <head>
        <meta charset='utf-8'/>
        <script src='https://unpkg.com/cytoscape/dist/cytoscape.min.js'></script>
        <style>body, html { margin:0; padding:0; background: ${background}; }</style>
      </head>
      <body>
        <div id='cyContainer' style='width:${width}px; height:${height}px;'></div>
        <script>
          document.addEventListener('DOMContentLoaded', function() {
            window.cy = cytoscape({
              container: document.getElementById('cyContainer'),
              elements: ${JSON.stringify(elements)},
              style: ${JSON.stringify(style)},
              layout: ${JSON.stringify(layout)}
            });
            window.cy.ready(function() {
              window.cyPng = window.cy.png({
                output: '${resolvesTo}',
                full: false,
                bg: '${background}',
                scale: 3,
                format: '${format}'
              });
            });
          });
        </script>
      </body>
    </html>
  `;

  const browser = await getBrowser();
  const page = await browser.newPage();
  await page.setViewport({ width, height });
  await page.setContent(html, { waitUntil: "networkidle0" });
  await page.waitForSelector("#cyContainer");
  await page.waitForFunction("window.cyPng !== undefined");
  const imageBase64 = await page.evaluate(() => {
    return (window as unknown as { cyPng: string }).cyPng;
  });
  await page.close();
  return imageBase64;
}
