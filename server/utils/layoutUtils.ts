import type { NodeSingular, LayoutOptions, NodeCollection } from "cytoscape";
/**
 * Transforms an adjacency matrix into Cytoscape-compatible format.
 * @param adjacencyMatrix - The adjacency matrix to transform.
 * @returns An object containing nodes and edges in Cytoscape format.
 */
export const transformAdjacencyMatrixToCytoscapeFormat = (
  adjacencyMatrix: Record<number, number[]>
) => {
  const nodes: { data: { id: number } }[] = [];
  const edges: { data: { source: number; target: number } }[] = [];

  for (const node in adjacencyMatrix) {
    const source = parseInt(node);
    nodes.push({ data: { id: source } });

    adjacencyMatrix[node].forEach((target) => {
      edges.push({ data: { source: source, target } });
    });
  }

  return { elements: { nodes, edges } };
};
// Layouts configuration
// Check https://js.cytoscape.org/#extensions/layout-extensions for more information

export const cytocapeLayouts: Record<string, unknown> = {
  /**@link https://js.cytoscape.org/#layouts/circle
   */
  circle: {
    name: "circle",
    spacingFactor: 50,
  },
  preset: {
    name: "preset",
  },
  /**@link https://js.cytoscape.org/#layouts/grid
   */
  grid: {
    name: "grid",
    avoidOverlapPadding: 150,
  },
  /**@link https://js.cytoscape.org/#layouts/breadthfirst
   */
  breadthfirst: {
    name: "breadthfirst",
    spacingFactor: 20,
  },
  /** @link https://js.cytoscape.org/#layouts/concentric
   */
  concentric: {
    name: "concentric",
    padding: 50,
    minNodeSpacing: 150,

    // Fixed concentric function - more robust degree calculation
    concentric: function (node: NodeSingular): number {
      const degree = node.degree(false); // false = don't include loops
      // Return degree, but ensure isolated nodes (degree 0) go to outer ring
      return degree > 0 ? degree : 0.1; // Small value pushes isolated nodes outward
    },

    // Fixed levelWidth function - proper typing and calculation
    levelWidth: function (nodes: NodeCollection): number {
      const maxDegree = nodes.maxDegree(false); // false = don't include loops
      // Better scaling for level width
      return maxDegree > 0 ? Math.max(2, maxDegree / 3) : 2;
    },

    // Additional useful options
    startAngle: 0, // Start angle in radians
    sweep: 2 * Math.PI, // Full circle
    clockwise: true, // Layout direction
    equidistant: false, // Space nodes evenly vs by degree
    animate: false, // Set to true for animated layout
  },
  /** @link https://js.cytoscape.org/#layouts/cose
   */
  cose: {
    name: "cose",
    animate: false,
    randomize: true,
    componentSpacing: 50,
    nodeOverlap: 1,
    nestingFactor: 1.2,
  },
  /** @link https://github.com/cytoscape/cytoscape.js-euler#api
   */
  euler: {
    name: "euler",
    animate: false,
    randomize: false, // Start from existing positions
    springLength: () => 100,
    springCoeff: () => 0.0002,
    gravity: -8, // Moderate gravity to maintain clusters
    mass: () => 12,
    theta: 0.6,
    dragCoeff: 0.1,
    movementThreshold: 1.5,
    maxIterations: 800,

    fit: false,
    padding: 30,
    pull: 0.002, // Slightly stronger for cluster cohesion
    push: 0.001,

    ungrabifyWhileSimulating: false,
    fixedAfterDragging: true,
  },
  /** @link https://github.com/cytoscape/cytoscape.js-dagre#api
   */
  dagre: {
    // Remapped to built-in breadthfirst to avoid external constructor issues in SSR
    name: "breadthfirst",
    directed: true,
    spacingFactor: 200,
    avoidOverlap: true,
    circle: false,
  },
  /** @link https://github.com/iVis-at-Bilkent/cytoscape.js-cise#api
   */
  cise: {
    name: "cise",
    animate: false,
    spacingFactor: 1.25,
  },
  /** @link https://github.com/iVis-at-Bilkent/cytoscape.js-fcose#api
   */
  fcose: {
    name: "fcose",
    animate: false,
    nodeSeparation: 175,
    nodeRepulsion: () => 1000000,
    idealEdgeLength: () => 500,
    tilingPaddingVertical: 500,
    tilingPaddingHorizontal: 500,
  },
  /** @link https://github.com/iVis-at-Bilkent/cytoscape.js-avsdf#api
   */
  avsdf: {
    name: "avsdf",
    animate: false,
    nodeSeparation: 200,
  },
  /** @link https://github.com/cytoscape/cytoscape.js-elk#api
   */
  elk: {
    name: "elk",
    animate: false,
    elk: {
      // All options are available at http://www.eclipse.org/elk/reference.html
      // 'org.eclipse.' can be dropped from the identifier. The subsequent identifier has to be used as property key in quotes.
      // E.g. for 'org.eclipse.elk.direction' use:
      // 'elk.direction'
      // Enums use the name of the enum as string e.g. instead of Direction.DOWN use:
      // 'elk.direction': 'DOWN'
      // The main field to set is `algorithm`, which controls which particular layout algorithm is used.
      algorithm: null,
      "elk.spacing.nodeNode": "50",
    },
  },
  /** @link https://github.com/cytoscape/cytoscape.js-klay#api
   */
  klay: {
    name: "klay",
    animate: false,
    klay: {
      spacing: 100,
    },
  },
  tidytree: {
    name: "tidytree",
  },
};

export const getCytoscapeLayoutByName = (name: string): LayoutOptions => {
  return (cytocapeLayouts[name] ? cytocapeLayouts[name] : []) as LayoutOptions; //
};
