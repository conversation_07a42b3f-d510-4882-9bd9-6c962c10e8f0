// Standalone Node worker to compute cose-bilkent positions using Cytoscape
// Reads JSON from stdin: { elements: ElementDefinition[] | {nodes, edges} }
// Writes JSON to stdout: { nodes: [{ id, position }] }

import cytoscapeDefault from "cytoscape";
import coseBilkent from "cytoscape-cose-bilkent";

const cytoscape = cytoscapeDefault.default || cytoscapeDefault;
const cose = coseBilkent.default || coseBilkent;

cytoscape.use(cose);

const readStdin = async () => {
  const chunks = [];
  for await (const chunk of process.stdin) {
    chunks.push(Buffer.from(chunk));
  }
  return Buffer.concat(chunks).toString("utf-8");
};

const toElementArray = (input) => {
  if (!input) return [];
  if (Array.isArray(input)) return input;
  const { nodes = [], edges = [] } = input.elements || input;
  return [...nodes, ...edges];
};

const main = async () => {
  try {
    const text = await readStdin();
    const elements = JSON.parse(text);
    const flatElements = toElementArray(elements);

    const cy = cytoscape({
      headless: true,
      elements: flatElements,
      styleEnabled: true,
    });

    const layout = cy.layout({
      name: "cose-bilkent",
      quality: "default",
      randomize: true,
      nodeRepulsion: 1000000,
      idealEdgeLength: 500,
      tilingPaddingVertical: 175,
      tilingPaddingHorizontal: 175,
      animate: false,
    });
    layout.run();

    const nodes = cy
      .nodes()
      .map((n) => ({ id: n.id(), position: n.position() }));
    const out = { nodes };

    process.stdout.write(JSON.stringify(out), () => process.exit(0));
  } catch (err) {
    process.stderr.write(String(err?.stack || err?.message || String(err)));
    process.exit(1);
  }
};

main();
